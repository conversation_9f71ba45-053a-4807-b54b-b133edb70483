#!/usr/bin/env python3
"""
Simple HTTP server to serve the webhook test page
This avoids CORS issues when testing the web interface
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def find_free_port(start_port=3000, max_attempts=10):
    """Find a free port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    return None

def serve_test_page():
    """Serve the webhook test page"""
    
    # Check if test page exists
    test_page = Path("webhook_test_page.html")
    if not test_page.exists():
        print("❌ webhook_test_page.html not found!")
        print("Please make sure you're running this script from the project root directory.")
        return False
    
    # Find a free port
    port = find_free_port()
    if not port:
        print("❌ Could not find a free port to serve the test page")
        return False
    
    print(f"🌐 Starting HTTP server for webhook test page...")
    print(f"📡 Server will run on: http://localhost:{port}")
    print(f"📄 Serving: {test_page.absolute()}")
    print(f"🔗 Test page URL: http://localhost:{port}/webhook_test_page.html")
    print()
    print("🚀 Make sure your FastAPI webhook server is running on http://localhost:8000")
    print("   You can start it with: python start_webhook_server.py")
    print()
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        # Create HTTP server
        handler = http.server.SimpleHTTPRequestHandler
        
        # Suppress default logging
        class QuietHandler(handler):
            def log_message(self, format, *args):
                # Only log errors
                if "404" in str(args) or "500" in str(args):
                    super().log_message(format, *args)
        
        with socketserver.TCPServer(("", port), QuietHandler) as httpd:
            print(f"✅ Server started successfully!")
            
            # Open browser automatically
            test_url = f"http://localhost:{port}/webhook_test_page.html"
            try:
                webbrowser.open(test_url)
                print(f"🌐 Opened {test_url} in your default browser")
            except Exception as e:
                print(f"⚠️  Could not open browser automatically: {e}")
                print(f"📖 Please manually open: {test_url}")
            
            print()
            print("📊 Server is ready for webhook testing!")
            
            # Serve forever
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        return True
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False

def main():
    """Main function"""
    print("🧪 Webhook Test Page Server")
    print("=" * 30)
    
    # Check if we're in the right directory
    if not Path("webhook_test_page.html").exists():
        print("❌ webhook_test_page.html not found!")
        print()
        print("Please run this script from the project root directory where")
        print("webhook_test_page.html is located.")
        print()
        print("Current directory:", os.getcwd())
        print("Expected file:", Path("webhook_test_page.html").absolute())
        sys.exit(1)
    
    # Start the server
    success = serve_test_page()
    
    if success:
        print("👋 Thanks for testing the webhook integration!")
    else:
        print("❌ Failed to start the test server")
        sys.exit(1)

if __name__ == "__main__":
    main()
