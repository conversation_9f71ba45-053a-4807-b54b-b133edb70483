# 🧪 Webhook Testing Guide

## Overview

This guide provides comprehensive instructions for testing all webhook endpoints using multiple methods: web interface, command line, and automated scripts.

## 🚀 Quick Start

### 1. Start the Webhook Server
```bash
# Method 1: Using the startup script
python start_webhook_server.py

# Method 2: Direct uvicorn command
cd backend
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 2. Open the Test Dashboard
```bash
# Open the web testing interface
open webhook_test_page.html
# Or navigate to: file:///path/to/your/project/webhook_test_page.html
```

### 3. Run Automated Tests
```bash
# Run the Python test script
python test_webhooks.py
```

## 🌐 Web Interface Testing

### Features of the Test Dashboard

**📊 Real-time Testing Dashboard**
- Interactive forms for all webhook endpoints
- Live response display with syntax highlighting
- Status indicators (success/error/pending)
- Bulk testing functionality
- Configuration management

### Step-by-Step Testing

#### 1. **Configuration Setup**
```
Base URL: http://localhost:8000
Webhook Secret: your-webhook-secret-key
```

#### 2. **Health Check** ✅
- **Purpose:** Verify API is running
- **Endpoint:** `GET /health`
- **Expected Response:**
```json
{
  "status": "ok"
}
```

#### 3. **Webhook Status** 📊
- **Purpose:** Check webhook configuration
- **Endpoint:** `GET /webhooks/status`
- **Expected Response:**
```json
{
  "webhook_secret_configured": true,
  "autotask_webhook_url_configured": false,
  "endpoints": {
    "inbound": "/webhooks/autotask/inbound",
    "assignment": "/webhooks/autotask/assignment",
    "notification": "/webhooks/autotask/notification"
  }
}
```

#### 4. **Webhook Integration Test** 🧪
- **Purpose:** Test data format without AI processing
- **Endpoint:** `POST /webhooks/test`
- **Sample Data:**
```json
{
  "title": "Test Email Server Issue",
  "description": "Email server appears to be down",
  "due_date": "2024-07-30",
  "priority": "High",
  "requester_email": "<EMAIL>"
}
```

#### 5. **Inbound Webhook (Full AI)** 🤖
- **Purpose:** Full ticket processing through AI agents
- **Endpoint:** `POST /webhooks/autotask/inbound`
- **Sample Data:**
```json
{
  "title": "Production Database Connection Issues",
  "description": "Multiple users cannot connect to database",
  "due_date": "2024-07-30",
  "priority": "Critical",
  "ticket_id": "AT-12345",
  "requester_name": "Sarah Johnson",
  "requester_email": "<EMAIL>"
}
```

#### 6. **Assignment Webhook** 👨‍💻
- **Purpose:** Send assignment data to Autotask
- **Endpoint:** `POST /webhooks/autotask/assignment`
- **Sample Data:**
```json
{
  "ticket_id": "AT-12345",
  "assigned_technician_name": "Alice Smith",
  "assigned_technician_email": "<EMAIL>",
  "assignment_notes": "Assigned based on database expertise",
  "estimated_hours": 3.0,
  "status": "Assigned"
}
```

#### 7. **Notification Webhook** 📧
- **Purpose:** Send notification data to Autotask
- **Endpoint:** `POST /webhooks/autotask/notification`
- **Sample Data:**
```json
{
  "ticket_id": "AT-12345",
  "notification_type": "assignment",
  "recipient_email": "<EMAIL>",
  "subject": "New Ticket Assignment",
  "message": "You have been assigned a new ticket"
}
```

## 💻 Command Line Testing

### Using cURL

#### Health Check
```bash
curl -X GET http://localhost:8000/health
```

#### Webhook Status
```bash
curl -X GET http://localhost:8000/webhooks/status
```

#### Test Webhook Integration
```bash
curl -X POST http://localhost:8000/webhooks/test \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Ticket",
    "description": "This is a test",
    "due_date": "2024-07-30",
    "priority": "Medium",
    "requester_email": "<EMAIL>"
  }'
```

#### Inbound Webhook
```bash
curl -X POST http://localhost:8000/webhooks/autotask/inbound \
  -H "Content-Type: application/json" \
  -H "X-Autotask-Signature: sha256=test-signature" \
  -d '{
    "title": "Database Issue",
    "description": "Cannot connect to database",
    "due_date": "2024-07-30",
    "priority": "Critical",
    "ticket_id": "AT-12345",
    "requester_name": "John Doe",
    "requester_email": "<EMAIL>"
  }'
```

#### Assignment Webhook
```bash
curl -X POST http://localhost:8000/webhooks/autotask/assignment \
  -H "Content-Type: application/json" \
  -d '{
    "ticket_id": "AT-12345",
    "assigned_technician_name": "Alice Smith",
    "assigned_technician_email": "<EMAIL>",
    "assignment_notes": "Database expert assigned",
    "estimated_hours": 4.0,
    "status": "Assigned"
  }'
```

#### Notification Webhook
```bash
curl -X POST http://localhost:8000/webhooks/autotask/notification \
  -H "Content-Type: application/json" \
  -d '{
    "ticket_id": "AT-12345",
    "notification_type": "assignment",
    "recipient_email": "<EMAIL>",
    "subject": "New Assignment",
    "message": "You have a new ticket assignment"
  }'
```

### Using HTTPie (Alternative)

```bash
# Install HTTPie
pip install httpie

# Test endpoints
http GET localhost:8000/health
http GET localhost:8000/webhooks/status

http POST localhost:8000/webhooks/test \
  title="Test Ticket" \
  description="Test description" \
  due_date="2024-07-30" \
  priority="Medium"
```

## 🔧 Troubleshooting

### Common Issues

#### 1. **Connection Refused**
```
Error: Connection refused
```
**Solution:**
- Ensure FastAPI server is running
- Check if port 8000 is available
- Verify base URL in configuration

#### 2. **CORS Errors (Web Interface)**
```
Error: CORS policy blocked
```
**Solution:**
- CORS is already enabled in the FastAPI app
- Try using a local HTTP server for the HTML file:
```bash
python -m http.server 3000
# Then open http://localhost:3000/webhook_test_page.html
```

#### 3. **Database Connection Errors**
```
Error: Failed to connect to Snowflake
```
**Solution:**
- Check Snowflake credentials in .env file
- Verify network connectivity
- Ensure SSO authentication is working

#### 4. **AI Agent Initialization Errors**
```
Error: Agent not initialized
```
**Solution:**
- Check all required environment variables
- Verify data reference files exist
- Check agent dependencies

### Debug Mode

Enable debug logging:
```bash
export DEBUG=true
uvicorn main:app --host 0.0.0.0 --port 8000 --reload --log-level debug
```

### Monitoring Logs

Watch real-time logs:
```bash
tail -f logs/webhook.log  # If logging to file
# Or check console output
```

## 📊 Expected Test Results

### Successful Response Format
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    "ticket_number": "TKT-2024-001",
    "assigned_technician": "Alice Smith",
    "processing_time": "2024-07-29T10:30:00Z"
  }
}
```

### Error Response Format
```json
{
  "success": false,
  "message": "Operation failed",
  "errors": [
    "Database connection failed",
    "Invalid ticket data"
  ]
}
```

## 🎯 Testing Checklist

- [ ] ✅ Health check responds with 200 OK
- [ ] 📊 Webhook status shows configuration
- [ ] 🧪 Test webhook validates data format
- [ ] 🤖 Inbound webhook processes through AI agents
- [ ] 👨‍💻 Assignment webhook sends data successfully
- [ ] 📧 Notification webhook sends data successfully
- [ ] 🚀 Bulk tests pass all endpoints
- [ ] 🔒 Security headers are present
- [ ] 📝 All responses follow expected format
- [ ] ⚡ Response times are acceptable (<5 seconds)

## 🔄 Continuous Testing

### Automated Testing Schedule
```bash
# Add to crontab for regular testing
0 */6 * * * /path/to/test_webhooks.py >> /var/log/webhook_tests.log 2>&1
```

### Monitoring Integration
- Set up alerts for failed webhook tests
- Monitor response times and error rates
- Track webhook usage patterns

This comprehensive testing approach ensures your webhook integration is robust and reliable!
