# Autotask Webhook Integration Guide

## Overview

This document explains how to set up and use webhook endpoints for bidirectional communication between your TeamLogic AI system and Autotask.

## Architecture

```
Autotask → [Inbound Webhook] → AI Agents → [Outbound Webhooks] → Autotask
```

### Workflow:
1. **Autotask creates ticket** → Sends POST request to inbound webhook
2. **AI Agents process ticket** → Classification, assignment, notification
3. **System sends results back** → Assignment and notification webhooks to Autotask

## Webhook Endpoints

### 1. Inbound Webhook (Receive from Autotask)
**Endpoint:** `POST /webhooks/autotask/inbound`

**Purpose:** Receives ticket data from Autotask when a new ticket is created.

**Request Format:**
```json
{
  "title": "Email server down",
  "description": "Users cannot send or receive emails since 9 AM",
  "due_date": "2024-07-30",
  "priority": "High",
  "ticket_id": "AT-12345",
  "requester_name": "<PERSON>",
  "requester_email": "<EMAIL>",
  "company_id": "COMP-001",
  "contact_id": "CONT-001"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Ticket processed successfully. Internal ticket number: TKT-2024-001",
  "data": {
    "internal_ticket_number": "TKT-2024-001",
    "autotask_ticket_id": "AT-12345",
    "assigned_technician": "Alice Smith",
    "technician_email": "<EMAIL>",
    "classification": {
      "issue_type": "Email",
      "priority": "High",
      "category": "Infrastructure"
    },
    "processing_time": "2024-07-29T10:30:00Z"
  }
}
```

### 2. Assignment Webhook (Send to Autotask)
**Endpoint:** `POST /webhooks/autotask/assignment`

**Purpose:** Sends technician assignment data back to Autotask.

**Request Format:**
```json
{
  "ticket_id": "AT-12345",
  "assigned_technician_name": "Alice Smith",
  "assigned_technician_email": "<EMAIL>",
  "assignment_notes": "Assigned based on Exchange expertise and current workload",
  "estimated_hours": 4.0,
  "status": "Assigned"
}
```

### 3. Notification Webhook (Send to Autotask)
**Endpoint:** `POST /webhooks/autotask/notification`

**Purpose:** Notifies Autotask about email notifications sent to customers/technicians.

**Request Format:**
```json
{
  "ticket_id": "AT-12345",
  "notification_type": "assignment",
  "recipient_email": "<EMAIL>",
  "subject": "New Ticket Assignment: Email server down",
  "message": "You have been assigned a new ticket...",
  "sent_at": "2024-07-29T10:30:00Z"
}
```

## Configuration

### Environment Variables

Create a `.env` file or set these environment variables:

```bash
# Webhook Security
WEBHOOK_SECRET=your-secure-webhook-secret-key

# Autotask Integration
AUTOTASK_WEBHOOK_URL=https://your-autotask-instance.com/api/webhooks
AUTOTASK_API_URL=https://your-autotask-instance.com/api
AUTOTASK_API_KEY=your-autotask-api-key

# Security Settings
VERIFY_WEBHOOK_SIGNATURES=true
WEBHOOK_ALLOWED_IPS=*************,*********

# Timeout Settings
WEBHOOK_TIMEOUT=30
WEBHOOK_RETRY_ATTEMPTS=3

# Logging
LOG_WEBHOOK_REQUESTS=true
LOG_WEBHOOK_RESPONSES=true
```

### Autotask Configuration

1. **Set up outbound webhook in Autotask:**
   - Go to Autotask Admin → Workflow Rules
   - Create new rule for "Ticket Created" event
   - Add action to send HTTP POST to: `https://your-domain.com/webhooks/autotask/inbound`

2. **Set up inbound webhook endpoints in Autotask:**
   - Configure endpoints to receive assignment and notification data
   - URLs: `/api/webhooks/assignment` and `/api/webhooks/notification`

## Security

### Webhook Signature Verification

All webhooks use HMAC-SHA256 signature verification:

**Outbound (to Autotask):**
- Header: `X-TeamLogic-Signature: sha256=<signature>`

**Inbound (from Autotask):**
- Header: `X-Autotask-Signature: sha256=<signature>`

### IP Whitelisting

Configure `WEBHOOK_ALLOWED_IPS` to restrict webhook access to specific IP addresses.

## Testing

### 1. Test Webhook Status
```bash
GET /webhooks/status
```

### 2. Test Webhook Integration
```bash
POST /webhooks/test
Content-Type: application/json

{
  "title": "Test ticket",
  "description": "This is a test ticket",
  "due_date": "2024-07-30",
  "priority": "Medium"
}
```

### 3. Manual Assignment Test
```bash
POST /webhooks/autotask/assignment
Content-Type: application/json

{
  "ticket_id": "TEST-123",
  "assigned_technician_name": "Test Technician",
  "assigned_technician_email": "<EMAIL>",
  "assignment_notes": "Test assignment",
  "status": "Assigned"
}
```

## Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
  "success": false,
  "message": "Invalid request data",
  "errors": ["Title and description are required"]
}
```

**401 Unauthorized:**
```json
{
  "success": false,
  "message": "Invalid webhook signature",
  "errors": ["Signature verification failed"]
}
```

**500 Internal Server Error:**
```json
{
  "success": false,
  "message": "Failed to process ticket",
  "errors": ["Database connection failed"]
}
```

## Monitoring

### Webhook Logs

All webhook requests and responses are logged when `LOG_WEBHOOK_REQUESTS=true`.

### Health Checks

Use `/webhooks/status` endpoint to monitor:
- Configuration status
- Autotask connectivity
- Security settings

## Troubleshooting

### Common Issues

1. **Signature verification fails:**
   - Check `WEBHOOK_SECRET` matches on both sides
   - Verify signature header format

2. **Autotask connectivity issues:**
   - Check `AUTOTASK_WEBHOOK_URL` is correct
   - Verify network connectivity and firewall rules

3. **Ticket processing fails:**
   - Check database connectivity
   - Verify AI agents are properly initialized

### Debug Mode

Set environment variable `DEBUG=true` for detailed logging.
