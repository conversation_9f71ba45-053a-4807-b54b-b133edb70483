# 🔗 Autotask Webhook Testing Suite

## 🎯 Overview

Complete testing suite for Autotask webhook integration with interactive web dashboard, automated scripts, and comprehensive documentation.

## 🚀 Quick Start (3 Steps)

### Step 1: Start the Webhook Server
```bash
python start_webhook_server.py
```
*This starts the FastAPI server with all webhook endpoints on http://localhost:8000*

### Step 2: Open the Test Dashboard
```bash
python serve_test_page.py
```
*This opens an interactive web dashboard for testing all endpoints*

### Step 3: Run Automated Tests
```bash
python test_webhooks.py
```
*This runs comprehensive automated tests of all webhook functionality*

## 📁 Testing Files

| File | Purpose | Usage |
|------|---------|-------|
| `webhook_test_page.html` | Interactive web dashboard | Visual testing interface |
| `serve_test_page.py` | HTTP server for test page | Serves the web dashboard |
| `test_webhooks.py` | Automated test script | Command-line testing |
| `start_webhook_server.py` | FastAPI server startup | Starts the webhook server |

## 🌐 Web Dashboard Features

### 📊 **Interactive Testing Interface**
- **Real-time Response Display** - See API responses instantly
- **Syntax Highlighting** - JSON responses with proper formatting  
- **Status Indicators** - Visual success/error indicators
- **Form Validation** - Input validation and error handling
- **Bulk Testing** - Test all endpoints in sequence

### 🔧 **Configuration Management**
- **Base URL Configuration** - Easy server URL switching
- **Webhook Secret Management** - Security configuration
- **Connection Testing** - Verify server connectivity

### 📋 **Available Test Endpoints**

1. **Health Check** (`GET /health`)
   - Basic API connectivity test
   - Verifies server is running

2. **Webhook Status** (`GET /webhooks/status`)
   - Configuration status check
   - Autotask connectivity test

3. **Webhook Integration Test** (`POST /webhooks/test`)
   - Data format validation
   - No AI processing (fast test)

4. **Inbound Webhook** (`POST /webhooks/autotask/inbound`)
   - Full AI agent processing
   - Classification, assignment, notification

5. **Assignment Webhook** (`POST /webhooks/autotask/assignment`)
   - Send assignment data to Autotask
   - Technician assignment testing

6. **Notification Webhook** (`POST /webhooks/autotask/notification`)
   - Send notification data to Autotask
   - Email notification testing

## 🧪 Testing Scenarios

### Scenario 1: Basic Connectivity
```
1. Start webhook server
2. Open test dashboard
3. Click "Test Connection"
4. Run "Health Check"
5. Check "Webhook Status"
```

### Scenario 2: Data Format Validation
```
1. Fill out "Webhook Integration Test" form
2. Click "Test Webhook Integration"
3. Verify response format
4. Check validation messages
```

### Scenario 3: Full AI Workflow
```
1. Fill out "Inbound Webhook" form with realistic data
2. Click "Test Inbound Webhook"
3. Wait for AI processing (may take 30-60 seconds)
4. Check assigned technician in response
5. Verify classification results
```

### Scenario 4: Outbound Webhooks
```
1. Test "Assignment Webhook" with sample data
2. Test "Notification Webhook" with sample data
3. Verify successful delivery responses
```

### Scenario 5: Bulk Testing
```
1. Click "Run All Tests" in bulk test section
2. Watch progress indicators
3. Review comprehensive results
4. Check success rate
```

## 💻 Command Line Testing

### Quick Tests
```bash
# Test server health
curl http://localhost:8000/health

# Test webhook status
curl http://localhost:8000/webhooks/status

# Test with sample data
curl -X POST http://localhost:8000/webhooks/test \
  -H "Content-Type: application/json" \
  -d '{"title":"Test","description":"Test ticket","due_date":"2024-07-30","priority":"Medium"}'
```

### Automated Test Suite
```bash
# Run all tests
python test_webhooks.py

# Expected output:
# 🚀 Starting Autotask Webhook Integration Tests
# ✅ Health Check passed
# ✅ Webhook Status passed
# ✅ Webhook Integration Test passed
# ✅ Assignment Webhook passed
# ✅ Notification Webhook passed
# 🎉 All tests passed!
```

## 🔧 Configuration

### Environment Variables
```bash
# Required for full functionality
SF_ACCOUNT=your-snowflake-account
SF_USER=your-snowflake-user
SF_WAREHOUSE=your-warehouse
SF_DATABASE=your-database
SF_SCHEMA=your-schema
SF_ROLE=your-role

# Optional webhook configuration
WEBHOOK_SECRET=your-webhook-secret-key
AUTOTASK_WEBHOOK_URL=https://your-autotask-instance.com/api/webhooks
```

### Server Configuration
```bash
# Default server settings
HOST=0.0.0.0
PORT=8000
RELOAD=true
LOG_LEVEL=info
```

## 🛠️ Troubleshooting

### Common Issues

#### Server Won't Start
```bash
# Check if port is in use
lsof -i :8000

# Try different port
uvicorn main:app --port 8001
```

#### CORS Errors in Web Dashboard
```bash
# Use the HTTP server
python serve_test_page.py

# Or serve manually
python -m http.server 3000
```

#### Database Connection Errors
```bash
# Check Snowflake credentials
python test_config.py

# Verify SSO connection
python test_sso_connection.py
```

#### AI Agent Errors
```bash
# Check agent initialization
python -c "from src.agents.intake_agent import IntakeClassificationAgent; print('Agents OK')"

# Verify data files
ls -la data/
```

### Debug Mode
```bash
# Enable debug logging
export DEBUG=true
python start_webhook_server.py
```

## 📊 Expected Results

### Successful Health Check
```json
{
  "status": "ok"
}
```

### Successful Webhook Status
```json
{
  "webhook_secret_configured": true,
  "autotask_webhook_url_configured": false,
  "endpoints": {
    "inbound": "/webhooks/autotask/inbound",
    "assignment": "/webhooks/autotask/assignment",
    "notification": "/webhooks/autotask/notification"
  }
}
```

### Successful Inbound Webhook
```json
{
  "success": true,
  "message": "Ticket processed successfully. Internal ticket number: TKT-2024-001",
  "data": {
    "internal_ticket_number": "TKT-2024-001",
    "assigned_technician": "Alice Smith",
    "technician_email": "<EMAIL>",
    "classification": {
      "issue_type": "Database",
      "priority": "Critical"
    }
  }
}
```

## 🎯 Testing Checklist

- [ ] ✅ Server starts without errors
- [ ] 🌐 Web dashboard loads correctly
- [ ] 🔗 Connection test passes
- [ ] 📊 Health check returns 200 OK
- [ ] ⚙️ Webhook status shows configuration
- [ ] 🧪 Test webhook validates data format
- [ ] 🤖 Inbound webhook processes through AI
- [ ] 👨‍💻 Assignment webhook sends successfully
- [ ] 📧 Notification webhook sends successfully
- [ ] 🚀 Bulk tests pass all endpoints
- [ ] 📝 All responses follow expected format
- [ ] ⚡ Response times are acceptable

## 🔄 Next Steps

1. **Configure Autotask Integration**
   - Set up outbound webhook in Autotask
   - Configure inbound webhook endpoints
   - Test with real Autotask data

2. **Production Deployment**
   - Set up proper webhook secrets
   - Configure HTTPS endpoints
   - Set up monitoring and logging

3. **Monitoring Setup**
   - Set up webhook failure alerts
   - Monitor response times
   - Track usage patterns

## 📚 Additional Resources

- [Webhook Integration Guide](docs/WEBHOOK_INTEGRATION.md)
- [Detailed Testing Guide](docs/WEBHOOK_TESTING_GUIDE.md)
- [API Documentation](http://localhost:8000/docs) (when server is running)

---

**🎉 Happy Testing!** Your webhook integration is ready for comprehensive testing.
