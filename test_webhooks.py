#!/usr/bin/env python3
"""
Test script for Autotask webhook integration
"""

import requests
import json
import time
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://localhost:8000"  # Change to your FastAPI server URL
WEBHOOK_SECRET = "your-webhook-secret-key"  # Should match your configuration

def test_webhook_status():
    """Test webhook status endpoint"""
    print("🔍 Testing webhook status...")
    
    try:
        response = requests.get(f"{BASE_URL}/webhooks/status")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_webhook_integration():
    """Test webhook integration with sample data"""
    print("\n🧪 Testing webhook integration...")
    
    test_data = {
        "title": "Test Email Server Issue",
        "description": "This is a test ticket to verify webhook integration. Email server appears to be down and users cannot send emails.",
        "due_date": (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d"),
        "priority": "High",
        "ticket_id": "TEST-001",
        "requester_name": "Test User",
        "requester_email": "<EMAIL>",
        "company_id": "COMP-TEST",
        "contact_id": "CONT-TEST"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/webhooks/test",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_inbound_webhook():
    """Test inbound webhook with full AI processing"""
    print("\n📥 Testing inbound webhook (full AI processing)...")
    
    test_data = {
        "title": "Production Database Connection Issues",
        "description": "Multiple users reporting they cannot connect to the production database. Error message: 'Connection timeout after 30 seconds'. This started around 2 PM today.",
        "due_date": (datetime.now() + timedelta(hours=4)).strftime("%Y-%m-%d"),
        "priority": "Critical",
        "ticket_id": "AT-12345",
        "requester_name": "Sarah Johnson",
        "requester_email": "<EMAIL>",
        "company_id": "COMP-001",
        "contact_id": "CONT-001"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/webhooks/autotask/inbound",
            json=test_data,
            headers={
                "Content-Type": "application/json",
                "X-Autotask-Signature": "sha256=test-signature"  # In production, generate proper signature
            }
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_assignment_webhook():
    """Test assignment webhook"""
    print("\n📤 Testing assignment webhook...")
    
    assignment_data = {
        "ticket_id": "AT-12345",
        "assigned_technician_name": "Alice Smith",
        "assigned_technician_email": "<EMAIL>",
        "assignment_notes": "Assigned based on database expertise and current availability",
        "estimated_hours": 3.0,
        "status": "Assigned"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/webhooks/autotask/assignment",
            json=assignment_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_notification_webhook():
    """Test notification webhook"""
    print("\n📧 Testing notification webhook...")
    
    notification_data = {
        "ticket_id": "AT-12345",
        "notification_type": "assignment",
        "recipient_email": "<EMAIL>",
        "subject": "New Ticket Assignment: Production Database Connection Issues",
        "message": "You have been assigned a new critical ticket regarding database connection issues...",
        "sent_at": datetime.now().isoformat()
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/webhooks/autotask/notification",
            json=notification_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all webhook tests"""
    print("🚀 Starting Autotask Webhook Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Webhook Status", test_webhook_status),
        ("Webhook Integration Test", test_webhook_integration),
        ("Inbound Webhook", test_inbound_webhook),
        ("Assignment Webhook", test_assignment_webhook),
        ("Notification Webhook", test_notification_webhook)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        success = test_func()
        results.append((test_name, success))
        
        if success:
            print(f"✅ {test_name} passed")
        else:
            print(f"❌ {test_name} failed")
        
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Webhook integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
