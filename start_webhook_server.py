#!/usr/bin/env python3
"""
Startup script for the FastAPI webhook server
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_configuration():
    """Check if configuration is properly set up"""
    print("🔍 Checking configuration...")
    
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env file not found. Creating template...")
        create_env_template()
    
    # Check critical environment variables
    critical_vars = [
        "SF_ACCOUNT",
        "SF_USER", 
        "SF_WAREHOUSE",
        "SF_DATABASE",
        "SF_SCHEMA",
        "SF_ROLE"
    ]
    
    missing_vars = []
    for var in critical_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("⚠️  Missing critical environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n💡 Please configure these in your .env file")
        return False
    
    return True

def create_env_template():
    """Create a template .env file"""
    template = """# Snowflake Configuration
SF_ACCOUNT=your-snowflake-account
SF_USER=your-snowflake-user
SF_WAREHOUSE=your-warehouse
SF_DATABASE=your-database
SF_SCHEMA=your-schema
SF_ROLE=your-role

# Webhook Configuration
WEBHOOK_SECRET=your-secure-webhook-secret-key
AUTOTASK_WEBHOOK_URL=https://your-autotask-instance.com/api/webhooks
AUTOTASK_API_URL=https://your-autotask-instance.com/api
AUTOTASK_API_KEY=your-autotask-api-key

# Security Settings
VERIFY_WEBHOOK_SIGNATURES=true
WEBHOOK_ALLOWED_IPS=

# Timeout Settings
WEBHOOK_TIMEOUT=30
WEBHOOK_RETRY_ATTEMPTS=3

# Logging
LOG_WEBHOOK_REQUESTS=true
LOG_WEBHOOK_RESPONSES=true
"""
    
    with open(".env", "w") as f:
        f.write(template)
    
    print("📝 Created .env template file. Please configure it with your settings.")

def start_server():
    """Start the FastAPI server"""
    print("🚀 Starting FastAPI webhook server...")
    
    # Change to backend directory
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return False
    
    os.chdir(backend_dir)
    
    # Start uvicorn server
    try:
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ]
        
        print("📡 Server starting on http://localhost:8000")
        print("📚 API documentation available at http://localhost:8000/docs")
        print("🔗 Webhook endpoints:")
        print("   - Inbound:     POST /webhooks/autotask/inbound")
        print("   - Assignment:  POST /webhooks/autotask/assignment")
        print("   - Notification: POST /webhooks/autotask/notification")
        print("   - Status:      GET  /webhooks/status")
        print("   - Test:        POST /webhooks/test")
        print("\n🛑 Press Ctrl+C to stop the server")
        print("="*60)
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("🔧 TeamLogic Autotask Webhook Server Startup")
    print("="*50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check configuration
    if not check_configuration():
        print("\n💡 Please configure your environment variables and try again.")
        sys.exit(1)
    
    print("✅ All checks passed!")
    print()
    
    # Start server
    start_server()

if __name__ == "__main__":
    main()
