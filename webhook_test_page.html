<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autotask Webhook Testing Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .config-section {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .config-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .config-row label {
            min-width: 120px;
            font-weight: bold;
            color: #495057;
        }
        
        .config-row input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .test-section {
            padding: 30px;
        }
        
        .endpoint-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 25px;
            overflow: hidden;
        }
        
        .endpoint-header {
            background: #495057;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .endpoint-method {
            background: #28a745;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .endpoint-method.post {
            background: #007bff;
        }
        
        .endpoint-method.get {
            background: #28a745;
        }
        
        .endpoint-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .response-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .response-section h4 {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .response-content {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background: #28a745;
        }
        
        .status-error {
            background: #dc3545;
        }
        
        .status-pending {
            background: #ffc107;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .config-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .config-row label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Autotask Webhook Testing Dashboard</h1>
            <p>Test all webhook endpoints and monitor responses in real-time</p>
        </div>
        
        <div class="config-section">
            <h3>⚙️ Configuration</h3>
            <div class="config-row">
                <label>Base URL:</label>
                <input type="text" id="baseUrl" value="http://localhost:8000" placeholder="http://localhost:8000">
                <button class="btn btn-success" onclick="testConnection()">Test Connection</button>
            </div>
            <div class="config-row">
                <label>Webhook Secret:</label>
                <input type="text" id="webhookSecret" value="your-webhook-secret-key" placeholder="your-webhook-secret-key">
            </div>
        </div>
        
        <div class="test-section">
            <!-- Health Check -->
            <div class="endpoint-card">
                <div class="endpoint-header">
                    <span>Health Check</span>
                    <span class="endpoint-method get">GET</span>
                </div>
                <div class="endpoint-body">
                    <p><strong>Endpoint:</strong> <code>/health</code></p>
                    <p>Basic health check to verify the API is running.</p>
                    <button class="btn" onclick="testHealthCheck()">Test Health Check</button>
                    <div id="health-response" class="response-section" style="display: none;">
                        <h4>Response:</h4>
                        <div class="response-content"></div>
                    </div>
                </div>
            </div>
            
            <!-- Webhook Status -->
            <div class="endpoint-card">
                <div class="endpoint-header">
                    <span>Webhook Status</span>
                    <span class="endpoint-method get">GET</span>
                </div>
                <div class="endpoint-body">
                    <p><strong>Endpoint:</strong> <code>/webhooks/status</code></p>
                    <p>Check webhook configuration and Autotask connectivity.</p>
                    <button class="btn" onclick="testWebhookStatus()">Test Webhook Status</button>
                    <div id="webhook-status-response" class="response-section" style="display: none;">
                        <h4>Response:</h4>
                        <div class="response-content"></div>
                    </div>
                </div>
            </div>
            
            <!-- Webhook Test -->
            <div class="endpoint-card">
                <div class="endpoint-header">
                    <span>Webhook Integration Test</span>
                    <span class="endpoint-method post">POST</span>
                </div>
                <div class="endpoint-body">
                    <p><strong>Endpoint:</strong> <code>/webhooks/test</code></p>
                    <p>Test webhook data format without full AI processing.</p>
                    
                    <div class="grid">
                        <div class="form-group">
                            <label>Title:</label>
                            <input type="text" id="test-title" value="Test Email Server Issue">
                        </div>
                        <div class="form-group">
                            <label>Priority:</label>
                            <select id="test-priority">
                                <option value="Low">Low</option>
                                <option value="Medium">Medium</option>
                                <option value="High" selected>High</option>
                                <option value="Critical">Critical</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Description:</label>
                        <textarea id="test-description">This is a test ticket to verify webhook integration. Email server appears to be down and users cannot send emails.</textarea>
                    </div>
                    
                    <div class="grid">
                        <div class="form-group">
                            <label>Due Date:</label>
                            <input type="date" id="test-due-date">
                        </div>
                        <div class="form-group">
                            <label>Requester Email:</label>
                            <input type="email" id="test-email" value="<EMAIL>">
                        </div>
                    </div>
                    
                    <button class="btn" onclick="testWebhookIntegration()">Test Webhook Integration</button>
                    <div id="webhook-test-response" class="response-section" style="display: none;">
                        <h4>Response:</h4>
                        <div class="response-content"></div>
                    </div>
                </div>
            </div>

            <!-- Inbound Webhook (Full AI Processing) -->
            <div class="endpoint-card">
                <div class="endpoint-header">
                    <span>Inbound Webhook (Full AI Processing)</span>
                    <span class="endpoint-method post">POST</span>
                </div>
                <div class="endpoint-body">
                    <p><strong>Endpoint:</strong> <code>/webhooks/autotask/inbound</code></p>
                    <p>Full webhook processing through AI agents for classification, assignment, and notification.</p>

                    <div class="grid">
                        <div class="form-group">
                            <label>Title:</label>
                            <input type="text" id="inbound-title" value="Production Database Connection Issues">
                        </div>
                        <div class="form-group">
                            <label>Priority:</label>
                            <select id="inbound-priority">
                                <option value="Low">Low</option>
                                <option value="Medium">Medium</option>
                                <option value="High">High</option>
                                <option value="Critical" selected>Critical</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Description:</label>
                        <textarea id="inbound-description">Multiple users reporting they cannot connect to the production database. Error message: 'Connection timeout after 30 seconds'. This started around 2 PM today and is affecting all departments.</textarea>
                    </div>

                    <div class="grid">
                        <div class="form-group">
                            <label>Due Date:</label>
                            <input type="date" id="inbound-due-date">
                        </div>
                        <div class="form-group">
                            <label>Autotask Ticket ID:</label>
                            <input type="text" id="inbound-ticket-id" value="AT-12345">
                        </div>
                    </div>

                    <div class="grid">
                        <div class="form-group">
                            <label>Requester Name:</label>
                            <input type="text" id="inbound-requester-name" value="Sarah Johnson">
                        </div>
                        <div class="form-group">
                            <label>Requester Email:</label>
                            <input type="email" id="inbound-requester-email" value="<EMAIL>">
                        </div>
                    </div>

                    <button class="btn" onclick="testInboundWebhook()">Test Inbound Webhook</button>
                    <div id="inbound-response" class="response-section" style="display: none;">
                        <h4>Response:</h4>
                        <div class="response-content"></div>
                    </div>
                </div>
            </div>

            <!-- Assignment Webhook -->
            <div class="endpoint-card">
                <div class="endpoint-header">
                    <span>Assignment Webhook</span>
                    <span class="endpoint-method post">POST</span>
                </div>
                <div class="endpoint-body">
                    <p><strong>Endpoint:</strong> <code>/webhooks/autotask/assignment</code></p>
                    <p>Send technician assignment data back to Autotask.</p>

                    <div class="grid">
                        <div class="form-group">
                            <label>Ticket ID:</label>
                            <input type="text" id="assignment-ticket-id" value="AT-12345">
                        </div>
                        <div class="form-group">
                            <label>Estimated Hours:</label>
                            <input type="number" id="assignment-hours" value="3.0" step="0.5">
                        </div>
                    </div>

                    <div class="grid">
                        <div class="form-group">
                            <label>Technician Name:</label>
                            <input type="text" id="assignment-tech-name" value="Alice Smith">
                        </div>
                        <div class="form-group">
                            <label>Technician Email:</label>
                            <input type="email" id="assignment-tech-email" value="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Assignment Notes:</label>
                        <textarea id="assignment-notes">Assigned based on database expertise and current availability. Technician has experience with similar database connectivity issues.</textarea>
                    </div>

                    <button class="btn" onclick="testAssignmentWebhook()">Test Assignment Webhook</button>
                    <div id="assignment-response" class="response-section" style="display: none;">
                        <h4>Response:</h4>
                        <div class="response-content"></div>
                    </div>
                </div>
            </div>

            <!-- Notification Webhook -->
            <div class="endpoint-card">
                <div class="endpoint-header">
                    <span>Notification Webhook</span>
                    <span class="endpoint-method post">POST</span>
                </div>
                <div class="endpoint-body">
                    <p><strong>Endpoint:</strong> <code>/webhooks/autotask/notification</code></p>
                    <p>Send notification data to Autotask about emails sent to customers/technicians.</p>

                    <div class="grid">
                        <div class="form-group">
                            <label>Ticket ID:</label>
                            <input type="text" id="notification-ticket-id" value="AT-12345">
                        </div>
                        <div class="form-group">
                            <label>Notification Type:</label>
                            <select id="notification-type">
                                <option value="assignment" selected>Assignment</option>
                                <option value="status_update">Status Update</option>
                                <option value="confirmation">Confirmation</option>
                                <option value="completion">Completion</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid">
                        <div class="form-group">
                            <label>Recipient Email:</label>
                            <input type="email" id="notification-recipient" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label>Subject:</label>
                            <input type="text" id="notification-subject" value="New Ticket Assignment: Production Database Issues">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Message:</label>
                        <textarea id="notification-message">You have been assigned a new critical ticket regarding database connection issues. Please review the ticket details and begin troubleshooting as soon as possible.</textarea>
                    </div>

                    <button class="btn" onclick="testNotificationWebhook()">Test Notification Webhook</button>
                    <div id="notification-response" class="response-section" style="display: none;">
                        <h4>Response:</h4>
                        <div class="response-content"></div>
                    </div>
                </div>
            </div>

            <!-- Bulk Test Section -->
            <div class="endpoint-card">
                <div class="endpoint-header">
                    <span>🚀 Bulk Test All Endpoints</span>
                    <span class="endpoint-method post">BULK</span>
                </div>
                <div class="endpoint-body">
                    <p>Run all webhook tests in sequence to verify complete integration.</p>
                    <button class="btn btn-warning" onclick="runBulkTests()">Run All Tests</button>
                    <div id="bulk-test-progress" style="margin-top: 15px; display: none;">
                        <div id="bulk-test-status"></div>
                        <div id="bulk-test-results" class="response-section" style="margin-top: 15px;">
                            <h4>Bulk Test Results:</h4>
                            <div class="response-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Set default due date to tomorrow
        document.addEventListener('DOMContentLoaded', function() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const dateString = tomorrow.toISOString().split('T')[0];

            // Set dates for all date inputs
            document.getElementById('test-due-date').value = dateString;
            document.getElementById('inbound-due-date').value = dateString;
        });

        // Utility function to make API calls
        async function makeApiCall(method, endpoint, data = null, headers = {}) {
            const baseUrl = document.getElementById('baseUrl').value.replace(/\/$/, '');
            const url = `${baseUrl}${endpoint}`;

            const config = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    ...headers
                }
            };

            if (data) {
                config.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, config);
                const responseData = await response.json();

                return {
                    status: response.status,
                    statusText: response.statusText,
                    data: responseData,
                    success: response.ok
                };
            } catch (error) {
                return {
                    status: 0,
                    statusText: 'Network Error',
                    data: { error: error.message },
                    success: false
                };
            }
        }

        // Display response in the UI
        function displayResponse(elementId, response) {
            const responseSection = document.getElementById(elementId);
            const responseContent = responseSection.querySelector('.response-content');

            const statusClass = response.success ? 'status-success' : 'status-error';
            const statusText = response.success ? 'SUCCESS' : 'ERROR';

            const formattedResponse = {
                status: response.status,
                statusText: response.statusText,
                timestamp: new Date().toISOString(),
                response: response.data
            };

            responseContent.innerHTML = `<span class="status-indicator ${statusClass}"></span>${statusText} (${response.status})\n\n${JSON.stringify(formattedResponse, null, 2)}`;
            responseSection.style.display = 'block';

            // Scroll to response
            responseSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        // Test connection to the API
        async function testConnection() {
            const response = await makeApiCall('GET', '/health');

            if (response.success) {
                alert('✅ Connection successful! API is running.');
            } else {
                alert(`❌ Connection failed: ${response.statusText}\n\nPlease check:\n1. Server is running\n2. Base URL is correct\n3. CORS is enabled`);
            }
        }

        // Test health check endpoint
        async function testHealthCheck() {
            const response = await makeApiCall('GET', '/health');
            displayResponse('health-response', response);
        }

        // Test webhook status endpoint
        async function testWebhookStatus() {
            const response = await makeApiCall('GET', '/webhooks/status');
            displayResponse('webhook-status-response', response);
        }

        // Test webhook integration
        async function testWebhookIntegration() {
            const data = {
                title: document.getElementById('test-title').value,
                description: document.getElementById('test-description').value,
                due_date: document.getElementById('test-due-date').value,
                priority: document.getElementById('test-priority').value,
                requester_email: document.getElementById('test-email').value,
                ticket_id: `TEST-${Date.now()}`,
                requester_name: "Test User",
                company_id: "COMP-TEST",
                contact_id: "CONT-TEST"
            };

            const response = await makeApiCall('POST', '/webhooks/test', data);
            displayResponse('webhook-test-response', response);
        }

        // Test inbound webhook (full AI processing)
        async function testInboundWebhook() {
            const data = {
                title: document.getElementById('inbound-title').value,
                description: document.getElementById('inbound-description').value,
                due_date: document.getElementById('inbound-due-date').value,
                priority: document.getElementById('inbound-priority').value,
                ticket_id: document.getElementById('inbound-ticket-id').value,
                requester_name: document.getElementById('inbound-requester-name').value,
                requester_email: document.getElementById('inbound-requester-email').value,
                company_id: "COMP-001",
                contact_id: "CONT-001"
            };

            const headers = {
                'X-Autotask-Signature': 'sha256=test-signature'  // In production, generate proper signature
            };

            const response = await makeApiCall('POST', '/webhooks/autotask/inbound', data, headers);
            displayResponse('inbound-response', response);
        }

        // Test assignment webhook
        async function testAssignmentWebhook() {
            const data = {
                ticket_id: document.getElementById('assignment-ticket-id').value,
                assigned_technician_name: document.getElementById('assignment-tech-name').value,
                assigned_technician_email: document.getElementById('assignment-tech-email').value,
                assignment_notes: document.getElementById('assignment-notes').value,
                estimated_hours: parseFloat(document.getElementById('assignment-hours').value),
                status: "Assigned"
            };

            const response = await makeApiCall('POST', '/webhooks/autotask/assignment', data);
            displayResponse('assignment-response', response);
        }

        // Test notification webhook
        async function testNotificationWebhook() {
            const data = {
                ticket_id: document.getElementById('notification-ticket-id').value,
                notification_type: document.getElementById('notification-type').value,
                recipient_email: document.getElementById('notification-recipient').value,
                subject: document.getElementById('notification-subject').value,
                message: document.getElementById('notification-message').value,
                sent_at: new Date().toISOString()
            };

            const response = await makeApiCall('POST', '/webhooks/autotask/notification', data);
            displayResponse('notification-response', response);
        }

        // Run bulk tests
        async function runBulkTests() {
            const progressDiv = document.getElementById('bulk-test-progress');
            const statusDiv = document.getElementById('bulk-test-status');
            const resultsDiv = document.getElementById('bulk-test-results').querySelector('.response-content');

            progressDiv.style.display = 'block';
            statusDiv.innerHTML = '<p>🚀 Starting bulk tests...</p>';

            const tests = [
                { name: 'Health Check', func: () => makeApiCall('GET', '/health') },
                { name: 'Webhook Status', func: () => makeApiCall('GET', '/webhooks/status') },
                { name: 'Webhook Test', func: () => makeApiCall('POST', '/webhooks/test', {
                    title: 'Bulk Test Ticket',
                    description: 'This is a bulk test ticket',
                    due_date: document.getElementById('test-due-date').value,
                    priority: 'Medium',
                    requester_email: '<EMAIL>'
                })},
                { name: 'Assignment Webhook', func: () => makeApiCall('POST', '/webhooks/autotask/assignment', {
                    ticket_id: 'BULK-TEST-001',
                    assigned_technician_name: 'Test Technician',
                    assigned_technician_email: '<EMAIL>',
                    assignment_notes: 'Bulk test assignment',
                    estimated_hours: 2.0,
                    status: 'Assigned'
                })},
                { name: 'Notification Webhook', func: () => makeApiCall('POST', '/webhooks/autotask/notification', {
                    ticket_id: 'BULK-TEST-001',
                    notification_type: 'assignment',
                    recipient_email: '<EMAIL>',
                    subject: 'Bulk Test Notification',
                    message: 'This is a bulk test notification',
                    sent_at: new Date().toISOString()
                })}
            ];

            const results = [];

            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                statusDiv.innerHTML = `<p>🔄 Running test ${i + 1}/${tests.length}: ${test.name}...</p>`;

                try {
                    const result = await test.func();
                    results.push({
                        name: test.name,
                        success: result.success,
                        status: result.status,
                        statusText: result.statusText
                    });

                    const status = result.success ? '✅' : '❌';
                    statusDiv.innerHTML += `<p>${status} ${test.name}: ${result.status} ${result.statusText}</p>`;
                } catch (error) {
                    results.push({
                        name: test.name,
                        success: false,
                        status: 0,
                        statusText: error.message
                    });
                    statusDiv.innerHTML += `<p>❌ ${test.name}: Error - ${error.message}</p>`;
                }

                // Brief pause between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // Display final results
            const passed = results.filter(r => r.success).length;
            const total = results.length;

            statusDiv.innerHTML += `<h4>📊 Final Results: ${passed}/${total} tests passed</h4>`;

            const summary = {
                timestamp: new Date().toISOString(),
                total_tests: total,
                passed: passed,
                failed: total - passed,
                success_rate: `${Math.round((passed / total) * 100)}%`,
                results: results
            };

            resultsDiv.innerHTML = JSON.stringify(summary, null, 2);

            if (passed === total) {
                statusDiv.innerHTML += '<p style="color: green; font-weight: bold;">🎉 All tests passed! Webhook integration is working correctly.</p>';
            } else {
                statusDiv.innerHTML += '<p style="color: orange; font-weight: bold;">⚠️ Some tests failed. Check individual results above.</p>';
            }
        }
    </script>
</body>
</html>
